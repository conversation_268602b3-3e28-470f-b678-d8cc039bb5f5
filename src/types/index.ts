import { ParamListBase } from '@react-navigation/native';

export interface RootStack extends Param<PERSON><PERSON>Base {
  home: undefined;
  settings: undefined;
}

export type PermissionStatus =
  | 'unavailable'
  | 'denied'
  | 'blocked'
  | 'granted'
  | 'limited';

export interface PermissionState {
  camera: PermissionStatus;
  audio: PermissionStatus;
  location: PermissionStatus;
  storage: PermissionStatus;
}

export interface NominatimAddress {
  road?: string;
  village?: string;
  county?: string;
  state_district?: string;
  state?: string;
  ISO3166_2_lvl4?: string;
  postcode?: string;
  country?: string;
  country_code?: string;
}

export interface AddressResponseType {
  place_id: number;
  licence: string;
  osm_type: string;
  osm_id: number;
  lat: string;
  lon: string;
  class: string;
  type: string;
  place_rank: number;
  importance: number;
  addresstype: string;
  name?: string;
  display_name: string;
  address: NominatimAddress;
  boundingbox: string[];
}
